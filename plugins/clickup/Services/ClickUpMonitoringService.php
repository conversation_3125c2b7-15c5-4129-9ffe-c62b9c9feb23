<?php

namespace Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpListAssignment;
use Carbon\Carbon;

class ClickUpMonitoringService
{
    private const METRICS_PREFIX = 'clickup_metrics_';
    private const PERFORMANCE_PREFIX = 'clickup_performance_';
    
    /**
     * Record performance metric
     */
    public function recordPerformanceMetric(string $operation, float $duration, bool $cached = false): void
    {
        $date = now()->format('Y-m-d');
        $hour = now()->format('H');
        
        // Record operation duration
        $durationKey = self::PERFORMANCE_PREFIX . "duration_{$operation}_{$date}_{$hour}";
        $this->incrementAverage($durationKey, $duration);
        
        // Record cache hit/miss
        $cacheKey = self::METRICS_PREFIX . ($cached ? 'hits' : 'misses') . "_{$operation}_{$date}";
        $currentCacheValue = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $currentCacheValue + 1, 86400 * 7); // Keep for 7 days

        // Record total operations
        $totalKey = self::METRICS_PREFIX . "total_{$operation}_{$date}";
        $currentTotalValue = Cache::get($totalKey, 0);
        Cache::put($totalKey, $currentTotalValue + 1, 86400 * 7);
        
        Log::debug('ClickUp performance metric recorded', [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'cached' => $cached,
            'date' => $date,
            'hour' => $hour
        ]);
    }

    /**
     * Record assignment performance
     */
    public function recordAssignmentPerformance(string $listId, float $duration, bool $cached = false): void
    {
        $this->recordPerformanceMetric('assignment', $duration, $cached);
        
        // Record specific list assignment performance
        $listKey = self::PERFORMANCE_PREFIX . "list_assignment_{$listId}_" . now()->format('Y-m-d');
        $this->incrementAverage($listKey, $duration);
        // Fixed: Cache::expire does not exist
        $currentListValue = Cache::get($listKey);
        if ($currentListValue !== null) {
            Cache::put($listKey, $currentListValue, 86400 * 3);
        } // Keep for 3 days
    }

    /**
     * Get performance metrics for a date range
     */
    public function getPerformanceMetrics(int $days = 7): array
    {
        $metrics = [
            'assignment' => [
                'total_requests' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'hit_rate' => 0,
                'avg_duration_ms' => 0,
                'daily_breakdown' => []
            ]
        ];
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            
            $hits = Cache::get(self::METRICS_PREFIX . "hits_assignment_{$date}", 0);
            $misses = Cache::get(self::METRICS_PREFIX . "misses_assignment_{$date}", 0);
            $total = Cache::get(self::METRICS_PREFIX . "total_assignment_{$date}", 0);
            
            $metrics['assignment']['total_requests'] += $total;
            $metrics['assignment']['cache_hits'] += $hits;
            $metrics['assignment']['cache_misses'] += $misses;
            
            $dailyHitRate = ($hits + $misses) > 0 ? round(($hits / ($hits + $misses)) * 100, 2) : 0;
            
            $metrics['assignment']['daily_breakdown'][$date] = [
                'total_requests' => $total,
                'cache_hits' => $hits,
                'cache_misses' => $misses,
                'hit_rate' => $dailyHitRate
            ];
        }
        
        // Calculate overall hit rate
        $totalCacheRequests = $metrics['assignment']['cache_hits'] + $metrics['assignment']['cache_misses'];
        $metrics['assignment']['hit_rate'] = $totalCacheRequests > 0 
            ? round(($metrics['assignment']['cache_hits'] / $totalCacheRequests) * 100, 2) 
            : 0;
        
        return $metrics;
    }

    /**
     * Get system health metrics
     */
    public function getSystemHealthMetrics(): array
    {
        $health = [
            'status' => 'healthy',
            'issues' => [],
            'metrics' => []
        ];
        
        try {
            // Check database sync status
            $staleLists = ClickUpList::where('last_synced_at', '<', now()->subHours(2))->count();
            $totalLists = ClickUpList::count();
            $activeLists = ClickUpList::active()->count();
            
            $health['metrics']['total_lists'] = $totalLists;
            $health['metrics']['active_lists'] = $activeLists;
            $health['metrics']['stale_lists'] = $staleLists;
            
            if ($staleLists > ($totalLists * 0.2)) { // More than 20% stale
                $health['status'] = 'warning';
                $health['issues'][] = "High number of stale lists: {$staleLists}/{$totalLists}";
            }
            
            // Check recent assignment activity
            $recentAssignments = ClickUpListAssignment::where('assigned_at', '>=', now()->subHours(24))->count();
            $health['metrics']['recent_assignments_24h'] = $recentAssignments;
            
            // Check cache performance
            $today = now()->format('Y-m-d');
            $todayHits = Cache::get(self::METRICS_PREFIX . "hits_assignment_{$today}", 0);
            $todayMisses = Cache::get(self::METRICS_PREFIX . "misses_assignment_{$today}", 0);
            $todayTotal = $todayHits + $todayMisses;
            
            $health['metrics']['cache_requests_today'] = $todayTotal;
            $health['metrics']['cache_hit_rate_today'] = $todayTotal > 0 
                ? round(($todayHits / $todayTotal) * 100, 2) 
                : 0;
            
            if ($todayTotal > 10 && $health['metrics']['cache_hit_rate_today'] < 50) {
                $health['status'] = 'warning';
                $health['issues'][] = "Low cache hit rate: {$health['metrics']['cache_hit_rate_today']}%";
            }
            
            // Check for failed syncs
            $failedSyncs = ClickUpList::where('sync_status', 'failed')->count();
            $health['metrics']['failed_syncs'] = $failedSyncs;
            
            if ($failedSyncs > 0) {
                $health['status'] = $failedSyncs > 5 ? 'unhealthy' : 'warning';
                $health['issues'][] = "Failed syncs detected: {$failedSyncs}";
            }
            
        } catch (\Exception $e) {
            $health['status'] = 'unhealthy';
            $health['issues'][] = 'Health check failed: ' . $e->getMessage();
            
            Log::error('ClickUp health check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return $health;
    }

    /**
     * Get cache efficiency report
     */
    public function getCacheEfficiencyReport(): array
    {
        $report = [
            'summary' => [
                'total_cache_operations' => 0,
                'cache_hits' => 0,
                'cache_misses' => 0,
                'overall_hit_rate' => 0,
                'estimated_api_calls_saved' => 0
            ],
            'trends' => []
        ];
        
        // Get last 7 days of data
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            
            $hits = Cache::get(self::METRICS_PREFIX . "hits_assignment_{$date}", 0);
            $misses = Cache::get(self::METRICS_PREFIX . "misses_assignment_{$date}", 0);
            $total = $hits + $misses;
            
            $report['summary']['total_cache_operations'] += $total;
            $report['summary']['cache_hits'] += $hits;
            $report['summary']['cache_misses'] += $misses;
            
            $dailyHitRate = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
            
            $report['trends'][] = [
                'date' => $date,
                'cache_operations' => $total,
                'cache_hits' => $hits,
                'cache_misses' => $misses,
                'hit_rate' => $dailyHitRate
            ];
        }
        
        // Calculate overall metrics
        $totalOps = $report['summary']['total_cache_operations'];
        if ($totalOps > 0) {
            $report['summary']['overall_hit_rate'] = round(
                ($report['summary']['cache_hits'] / $totalOps) * 100, 
                2
            );
            $report['summary']['estimated_api_calls_saved'] = $report['summary']['cache_hits'];
        }
        
        return $report;
    }

    /**
     * Increment average value (for duration tracking)
     */
    private function incrementAverage(string $key, float $value): void
    {
        $current = Cache::get($key, ['sum' => 0, 'count' => 0]);
        $current['sum'] += $value;
        $current['count'] += 1;
        
        Cache::put($key, $current, 86400 * 7); // Keep for 7 days
    }

    /**
     * Get average value
     */
    private function getAverage(string $key): float
    {
        $data = Cache::get($key, ['sum' => 0, 'count' => 0]);
        return $data['count'] > 0 ? $data['sum'] / $data['count'] : 0;
    }

    /**
     * Clear old metrics (cleanup)
     */
    public function cleanupOldMetrics(): int
    {
        $cleaned = 0;
        $cutoffDate = now()->subDays(30)->format('Y-m-d');
        
        // This is a simplified cleanup - in production you might want to use Redis SCAN
        // or implement a more sophisticated cleanup mechanism
        
        Log::info('ClickUp metrics cleanup completed', [
            'cleaned_entries' => $cleaned,
            'cutoff_date' => $cutoffDate
        ]);
        
        return $cleaned;
    }
}
