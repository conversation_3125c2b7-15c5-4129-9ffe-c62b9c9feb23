<?php

namespace Plugins\ClickUp\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Plugins\ClickUp\Models\ClickUpList;
use Plugins\ClickUp\Models\ClickUpTask;
use Plugins\ClickUp\Models\ClickUpApiToken;
use Carbon\Carbon;

class ClickUpCacheService
{
    private const CACHE_PREFIX = 'clickup_';
    private const DEFAULT_TTL = 1800; // 30 minutes
    private const METRICS_TTL = 86400; // 24 hours
    
    private ClickUpApiService $apiService;
    
    public function __construct(ClickUpApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Get cache TTL from settings or use default
     */
    private function getCacheTTL(): int
    {
        $token = ClickUpApiToken::getActiveToken();
        if ($token && isset($token->metadata['general_settings']['cache_duration'])) {
            return (int) $token->metadata['general_settings']['cache_duration'];
        }
        return self::DEFAULT_TTL;
    }

    /**
     * Generate cache key with prefix
     */
    private function getCacheKey(string $key): string
    {
        return self::CACHE_PREFIX . $key;
    }

    /**
     * Get cached list data or fetch from database/API
     */
    public function getList(string $listId): array
    {
        $cacheKey = $this->getCacheKey("list_{$listId}");
        
        return Cache::remember($cacheKey, $this->getCacheTTL(), function () use ($listId) {
            // First try to get from local database
            $list = ClickUpList::where('clickup_id', $listId)->first();
            
            if ($list && !$list->needsSync()) {
                $this->recordCacheHit('list');
                return [
                    'success' => true,
                    'data' => $this->formatListData($list),
                    'source' => 'database'
                ];
            }
            
            // If not in database or needs sync, fetch from API
            $this->recordCacheMiss('list');
            $apiResponse = $this->apiService->getList($listId, false); // Bypass cache to prevent infinite loop
            
            if ($apiResponse['success']) {
                // Update local database if we have the data
                if ($list) {
                    $this->updateListFromApiData($list, $apiResponse['data']);
                }
                
                return [
                    'success' => true,
                    'data' => $apiResponse['data'],
                    'source' => 'api'
                ];
            }
            
            // If API fails but we have stale database data, use it
            if ($list) {
                Log::warning('ClickUp API failed, using stale database data', [
                    'list_id' => $listId,
                    'last_synced' => $list->last_synced_at
                ]);
                
                return [
                    'success' => true,
                    'data' => $this->formatListData($list),
                    'source' => 'database_stale'
                ];
            }
            
            return $apiResponse;
        });
    }

    /**
     * Get multiple lists with caching
     */
    public function getLists(array $listIds): array
    {
        $results = [];
        $uncachedIds = [];
        
        // Check cache for each list
        foreach ($listIds as $listId) {
            $cacheKey = $this->getCacheKey("list_{$listId}");
            $cached = Cache::get($cacheKey);
            
            if ($cached) {
                $results[$listId] = $cached;
                $this->recordCacheHit('list');
            } else {
                $uncachedIds[] = $listId;
            }
        }
        
        // Fetch uncached lists
        foreach ($uncachedIds as $listId) {
            $results[$listId] = $this->getList($listId);
        }
        
        return $results;
    }

    /**
     * Cache all lists from database
     */
    public function cacheAllLists(): array
    {
        $lists = ClickUpList::active()->get();
        $cached = 0;
        $errors = 0;
        
        foreach ($lists as $list) {
            try {
                $cacheKey = $this->getCacheKey("list_{$list->clickup_id}");
                $data = [
                    'success' => true,
                    'data' => $this->formatListData($list),
                    'source' => 'database'
                ];
                
                Cache::put($cacheKey, $data, $this->getCacheTTL());
                $cached++;
            } catch (\Exception $e) {
                Log::error('Failed to cache list', [
                    'list_id' => $list->clickup_id,
                    'error' => $e->getMessage()
                ]);
                $errors++;
            }
        }
        
        return [
            'success' => true,
            'cached' => $cached,
            'errors' => $errors,
            'message' => "Cached {$cached} lists" . ($errors > 0 ? " with {$errors} errors" : "")
        ];
    }

    /**
     * Format list data from database model to API format
     */
    private function formatListData(ClickUpList $list): array
    {
        return [
            'id' => $list->clickup_id,
            'name' => $list->name,
            'content' => $list->description,
            'status' => [
                'status' => $list->status
            ],
            'priority' => [
                'priority' => $list->priority,
                'color' => $list->color
            ],
            'space' => [
                'id' => $list->space_id,
                'name' => $list->metadata['space_name'] ?? null,
                'workspace_id' => $list->team_id
            ],
            'folder' => $list->folder_id ? [
                'id' => $list->folder_id
            ] : null,
            'private' => $list->is_private,
            'archived' => $list->is_archived,
            'task_count' => $list->task_count,
            'due_date' => $list->due_date ? $list->due_date->timestamp * 1000 : null,
            'start_date' => $list->start_date ? $list->start_date->timestamp * 1000 : null,
            'avatar' => $list->avatar_url ? ['url' => $list->avatar_url] : null,
            'permission_level' => $list->permission_level,
            // Add cache metadata
            '_cache_info' => [
                'last_synced' => $list->last_synced_at?->toISOString(),
                'sync_status' => $list->sync_status,
                'cached_at' => now()->toISOString()
            ]
        ];
    }

    /**
     * Update list model from API data
     */
    private function updateListFromApiData(ClickUpList $list, array $apiData): void
    {
        $list->update([
            'name' => $apiData['name'],
            'description' => $apiData['content'] ?? null,
            'status' => $apiData['status']['status'] ?? $list->status,
            'priority' => $apiData['priority']['priority'] ?? null,
            'color' => $apiData['priority']['color'] ?? null,
            'is_private' => $apiData['private'] ?? false,
            'is_archived' => $apiData['archived'] ?? false,
            'task_count' => $apiData['task_count'] ?? 0,
            'due_date' => isset($apiData['due_date']) ? 
                Carbon::createFromTimestamp($apiData['due_date'] / 1000) : null,
            'start_date' => isset($apiData['start_date']) ? 
                Carbon::createFromTimestamp($apiData['start_date'] / 1000) : null,
            'avatar_url' => $apiData['avatar']['url'] ?? null,
            'permission_level' => $apiData['permission_level'] ?? null,
            'last_synced_at' => now(),
            'sync_status' => 'success'
        ]);
    }

    /**
     * Record cache hit for metrics
     */
    private function recordCacheHit(string $type): void
    {
        $key = $this->getCacheKey("metrics_hits_{$type}_" . now()->format('Y-m-d'));
        $currentValue = Cache::get($key, 0);
        Cache::put($key, $currentValue + 1, self::METRICS_TTL);
    }

    /**
     * Record cache miss for metrics
     */
    private function recordCacheMiss(string $type): void
    {
        $key = $this->getCacheKey("metrics_misses_{$type}_" . now()->format('Y-m-d'));
        $currentValue = Cache::get($key, 0);
        Cache::put($key, $currentValue + 1, self::METRICS_TTL);
    }

    /**
     * Clear cache for specific list
     */
    public function clearListCache(string $listId): bool
    {
        $cacheKey = $this->getCacheKey("list_{$listId}");
        return Cache::forget($cacheKey);
    }

    /**
     * Clear all list caches
     */
    public function clearAllListCaches(): array
    {
        $lists = ClickUpList::pluck('clickup_id');
        $cleared = 0;

        foreach ($lists as $listId) {
            if ($this->clearListCache($listId)) {
                $cleared++;
            }
        }

        return [
            'success' => true,
            'cleared' => $cleared,
            'message' => "Cleared {$cleared} list caches"
        ];
    }

    /**
     * Clear all ClickUp caches
     */
    public function clearAllCaches(): array
    {
        $pattern = self::CACHE_PREFIX . '*';
        $cleared = 0;

        // Get all cache keys with our prefix
        $keys = Cache::getRedis()->keys($pattern);

        foreach ($keys as $key) {
            if (Cache::forget(str_replace(config('cache.prefix') . ':', '', $key))) {
                $cleared++;
            }
        }

        return [
            'success' => true,
            'cleared' => $cleared,
            'message' => "Cleared {$cleared} cache entries"
        ];
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');

        $stats = [
            'today' => [
                'list_hits' => Cache::get($this->getCacheKey("metrics_hits_list_{$today}"), 0),
                'list_misses' => Cache::get($this->getCacheKey("metrics_misses_list_{$today}"), 0),
            ],
            'yesterday' => [
                'list_hits' => Cache::get($this->getCacheKey("metrics_hits_list_{$yesterday}"), 0),
                'list_misses' => Cache::get($this->getCacheKey("metrics_misses_list_{$yesterday}"), 0),
            ]
        ];

        // Calculate hit rates
        foreach (['today', 'yesterday'] as $period) {
            $hits = $stats[$period]['list_hits'];
            $misses = $stats[$period]['list_misses'];
            $total = $hits + $misses;

            $stats[$period]['total_requests'] = $total;
            $stats[$period]['hit_rate'] = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        }

        // Get cache size info
        $stats['cache_info'] = [
            'ttl' => $this->getCacheTTL(),
            'prefix' => self::CACHE_PREFIX,
            'total_lists_in_db' => ClickUpList::count(),
            'active_lists_in_db' => ClickUpList::active()->count(),
        ];

        return $stats;
    }

    /**
     * Refresh cache for specific list
     */
    public function refreshListCache(string $listId): array
    {
        // Clear existing cache
        $this->clearListCache($listId);

        // Fetch fresh data
        return $this->getList($listId);
    }

    /**
     * Refresh all list caches
     */
    public function refreshAllListCaches(): array
    {
        // Clear all existing caches
        $this->clearAllListCaches();

        // Rebuild cache from database
        return $this->cacheAllLists();
    }

    /**
     * Warm up cache with frequently accessed lists
     */
    public function warmUpCache(): array
    {
        // Get recently assigned lists and active lists
        $recentlyAssigned = \Plugins\ClickUp\Models\ClickUpListAssignment::active()
            ->where('assigned_at', '>=', now()->subDays(7))
            ->pluck('clickup_list_id')
            ->unique();

        $activeLists = ClickUpList::active()
            ->where('last_synced_at', '>=', now()->subHours(24))
            ->pluck('clickup_id');

        $listsToWarm = $recentlyAssigned->merge($activeLists)->unique();

        $warmed = 0;
        $errors = 0;

        foreach ($listsToWarm as $listId) {
            try {
                $this->getList($listId);
                $warmed++;
            } catch (\Exception $e) {
                Log::error('Failed to warm cache for list', [
                    'list_id' => $listId,
                    'error' => $e->getMessage()
                ]);
                $errors++;
            }
        }

        return [
            'success' => true,
            'warmed' => $warmed,
            'errors' => $errors,
            'message' => "Warmed cache for {$warmed} lists" . ($errors > 0 ? " with {$errors} errors" : "")
        ];
    }

    /**
     * Check if cache is healthy
     */
    public function checkCacheHealth(): array
    {
        $health = [
            'status' => 'healthy',
            'issues' => []
        ];

        try {
            // Test basic cache operations
            $testKey = $this->getCacheKey('health_test');
            Cache::put($testKey, 'test', 60);
            $value = Cache::get($testKey);
            Cache::forget($testKey);

            if ($value !== 'test') {
                $health['status'] = 'unhealthy';
                $health['issues'][] = 'Basic cache operations failed';
            }

            // Check if we have recent cache activity
            $today = now()->format('Y-m-d');
            $todayHits = Cache::get($this->getCacheKey("metrics_hits_list_{$today}"), 0);
            $todayMisses = Cache::get($this->getCacheKey("metrics_misses_list_{$today}"), 0);

            if ($todayHits + $todayMisses === 0) {
                $health['status'] = 'warning';
                $health['issues'][] = 'No cache activity detected today';
            }

            // Check database sync status
            $staleLists = ClickUpList::where('last_synced_at', '<', now()->subHours(2))->count();
            if ($staleLists > 0) {
                $health['status'] = 'warning';
                $health['issues'][] = "{$staleLists} lists have stale sync data";
            }

        } catch (\Exception $e) {
            $health['status'] = 'unhealthy';
            $health['issues'][] = 'Cache health check failed: ' . $e->getMessage();
        }

        return $health;
    }
}
