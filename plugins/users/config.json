{"name": "users", "version": "1.0.0", "enabled": true, "dependencies": [], "description": "Handles user authentication and access control", "system": true, "permissions": ["manage_users", "manage_roles", "manage_permissions"], "navigation": {"label": "User Management", "icon": "fas fa-users", "route": "users.index", "permissions": ["manage_users"], "subnav": [{"label": "Users", "icon": "fas fa-user", "route": "users.index", "permissions": ["manage_users"]}, {"label": "Roles", "icon": "fas fa-user-tag", "route": "roles.index", "permissions": ["manage_roles"]}, {"label": "Permissions", "icon": "fas fa-key", "route": "permissions.index", "permissions": ["manage_permissions"]}]}}