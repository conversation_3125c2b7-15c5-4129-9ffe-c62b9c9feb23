#!/bin/bash

# Performance Optimization Script for Laravel Application
# This script optimizes the application for production performance

echo "🚀 Starting Laravel Performance Optimization..."

# Clear all caches first
echo "🧹 Clearing existing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize Composer autoloader
echo "📦 Optimizing Composer autoloader..."
composer install --optimize-autoloader --no-dev

# Cache Laravel components
echo "⚡ Caching Laravel components..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Build optimized assets
echo "🎨 Building optimized assets..."
npm run build

# Clear and warm up application cache
echo "🔥 Warming up application cache..."
php artisan cache:clear

# Run a test request to warm up the navigation cache
echo "🌡️ Warming up navigation cache..."
php artisan tinker --execute="
\$user = \App\Models\User::first();
if (\$user) {
    \App\Models\NavigationMenu::getOptimizedNavigationTree(\$user, []);
    echo 'Navigation cache warmed up successfully';
} else {
    echo 'No users found to warm up cache';
}
"

echo "✅ Performance optimization completed!"
echo ""
echo "📊 Performance improvements applied:"
echo "   ✓ Fixed N+1 queries in navigation menu loading"
echo "   ✓ Added caching for user permissions and plugin data"
echo "   ✓ Optimized asset loading (removed CDN Tailwind)"
echo "   ✓ Added database indexes for frequently queried columns"
echo "   ✓ Cached routes, config, and views"
echo "   ✓ Optimized Composer autoloader"
echo ""
echo "🔧 To maintain performance:"
echo "   • Run this script after each deployment"
echo "   • Monitor cache hit rates"
echo "   • Clear caches when navigation/permissions change"
echo "   • Use 'php artisan cache:clear' if you see stale data"
