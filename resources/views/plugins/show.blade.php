@extends('layouts.app')

@section('content')
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <nav class="flex" aria-label="Breadcrumb">
                        <ol class="flex items-center space-x-4">
                            <li>
                                <a href="{{ route('plugins.index') }}" class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span class="sr-only">Plugins</span>
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 dark:text-gray-500 mr-4"></i>
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $plugin->name }}</span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mt-2 mb-2">
                        {{ $plugin->name }}
                        <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $plugin->enabled ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' }}">
                            {{ $plugin->enabled ? 'Enabled' : 'Disabled' }}
                        </span>
                        @if($plugin->isSystemPlugin())
                            <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                <i class="fas fa-shield-alt mr-2"></i>
                                System Plugin
                            </span>
                        @endif
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400">{{ $plugin->description ?: 'No description available' }}</p>
                </div>
                <div class="flex space-x-3">
                    <!-- Export Plugin -->
                    <a href="{{ route('plugins.export', $plugin->name) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </a>

                    @if($plugin->enabled)
                        @if($plugin->isSystemPlugin())
                            <button type="button" class="inline-flex items-center px-4 py-2 bg-gray-400 text-white font-medium rounded-md cursor-not-allowed" disabled title="System plugins cannot be disabled">
                                <i class="fas fa-lock mr-2"></i>
                                System Plugin
                            </button>
                        @else
                            <form method="POST" action="{{ route('plugins.disable', $plugin->name) }}" class="inline">
                                @csrf
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-150 ease-in-out" onclick="return confirm('Are you sure you want to disable this plugin?')">
                                    <i class="fas fa-stop mr-2"></i>
                                    Disable Plugin
                                </button>
                            </form>
                        @endif
                    @else
                        <form method="POST" action="{{ route('plugins.enable', $plugin->name) }}" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out" {{ !$plugin->isValid() ? 'disabled' : '' }}>
                                <i class="fas fa-play mr-2"></i>
                                Enable Plugin
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Plugin Information</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Basic details about this plugin</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <dl>
                            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $plugin->name }}</dd>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Version</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $plugin->version }}</dd>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $plugin->enabled ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200' }}">
                                        {{ $plugin->enabled ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </dd>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">{{ $plugin->description ?: 'No description provided' }}</dd>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Path</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    <code class="bg-gray-100 dark:bg-gray-600 dark:text-gray-200 px-2 py-1 rounded text-xs">{{ $plugin->path }}</code>
                                </dd>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Valid</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    @if($plugin->isValid())
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                            <i class="fas fa-check mr-1"></i>
                                            Valid
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                            <i class="fas fa-times mr-1"></i>
                                            Invalid
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Dependencies -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Dependencies</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Plugins that this plugin depends on</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if(empty($plugin->dependencies))
                            <div class="px-4 py-5 text-center text-gray-500 dark:text-gray-400">
                                <i class="fas fa-info-circle text-2xl mb-2"></i>
                                <p>This plugin has no dependencies</p>
                            </div>
                        @else
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($plugin->dependencies as $dependency)
                                    <li class="px-4 py-4 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-puzzle-piece text-gray-400 dark:text-gray-500 mr-3"></i>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $dependency }}</span>
                                        </div>
                                        <div>
                                            <!-- You could add dependency status here -->
                                            <span class="text-xs text-gray-500 dark:text-gray-400">Required</span>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>

                <!-- Permissions Management -->
                @if(!empty($plugin->permissions))
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Plugin Permissions</h3>
                                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Permissions defined in this plugin's configuration</p>
                                </div>
                                <button onclick="syncPluginPermissions('{{ $plugin->name }}')"
                                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                    <i class="fas fa-sync mr-2"></i>
                                    Sync Permissions
                                </button>
                            </div>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700">
                            <div class="px-4 py-4">
                                <div class="grid grid-cols-1 gap-3">
                                    @foreach($plugin->permissions as $permission)
                                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                                            <div class="flex items-center">
                                                <i class="fas fa-key text-blue-600 dark:text-blue-400 mr-3"></i>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $permission }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        @php
                                                            $existingPermission = \App\Models\Permission::where('name', $permission)->first();
                                                        @endphp
                                                        @if($existingPermission)
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                                <i class="fas fa-check mr-1"></i>
                                                                Exists in database
                                                            </span>
                                                        @else
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                                                Not in database
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            @if($existingPermission)
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    <div>{{ $existingPermission->display_name ?? 'No display name' }}</div>
                                                    @if($existingPermission->description)
                                                        <div class="mt-1">{{ Str::limit($existingPermission->description, 50) }}</div>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                    <div class="flex items-start">
                                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2 mt-0.5"></i>
                                        <div class="text-sm text-blue-800 dark:text-blue-200">
                                            <strong>Sync Permissions</strong> will:
                                            <ul class="mt-1 ml-4 list-disc text-xs">
                                                <li>Create missing permissions in the database</li>
                                                <li>Assign permissions to the admin role automatically</li>
                                                <li>Generate appropriate display names and descriptions</li>
                                                <li>Set the plugin category for organization</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Navigation Management -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Plugin Navigation</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Navigation menu configuration for this plugin</p>
                            </div>
                            @if(isset($plugin->config['navigation']) && !empty($plugin->config['navigation']))
                                <button onclick="syncPluginNavigation('{{ $plugin->name }}')"
                                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                                    <i class="fas fa-sync mr-2"></i>
                                    Sync Navigation
                                </button>
                            @endif
                        </div>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if(isset($plugin->config['navigation']) && !empty($plugin->config['navigation']))
                            <div class="px-4 py-4">
                                @php
                                    $navConfig = $plugin->config['navigation'];
                                    $existingNavItems = \App\Models\NavigationMenu::where('plugin', $plugin->name)->get();

                                    // Find main navigation item
                                    $mainNavItem = $existingNavItems->first(function($existing) use ($navConfig, $plugin) {
                                        return $existing->route === ($navConfig['route'] ?? '') ||
                                               $existing->label === ($navConfig['label'] ?? '') ||
                                               $existing->name === ($navConfig['label'] ?? $plugin->name);
                                    });
                                @endphp

                                <div class="space-y-4">
                                    <!-- Main Navigation Item -->
                                    <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-start">
                                                <i class="{{ $navConfig['icon'] ?? 'fas fa-circle' }} text-blue-600 dark:text-blue-400 mr-3 mt-1"></i>
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2 mb-2">
                                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                            {{ $navConfig['label'] ?? 'No label' }}
                                                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(Main Menu)</span>
                                                        </h4>
                                                        @if($mainNavItem)
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                                <i class="fas fa-check mr-1"></i>
                                                                Exists in navigation
                                                            </span>
                                                        @else
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                                                Not in navigation
                                                            </span>
                                                        @endif
                                                    </div>

                                                    <div class="grid grid-cols-2 gap-4 text-xs text-gray-600 dark:text-gray-400">
                                                        <div>
                                                            <span class="font-medium">Route:</span>
                                                            <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ $navConfig['route'] ?: 'Parent only (no route)' }}</code>
                                                        </div>
                                                        <div>
                                                            <span class="font-medium">Icon:</span>
                                                            <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ $navConfig['icon'] ?? 'Not specified' }}</code>
                                                        </div>
                                                        @if(isset($navConfig['permissions']) && !empty($navConfig['permissions']))
                                                            <div class="col-span-2">
                                                                <span class="font-medium">Permissions:</span>
                                                                <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ implode(', ', $navConfig['permissions']) }}</code>
                                                            </div>
                                                        @endif
                                                        @if(isset($navConfig['subnav']) && !empty($navConfig['subnav']))
                                                            <div class="col-span-2">
                                                                <span class="font-medium">Sub-items:</span>
                                                                <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ count($navConfig['subnav']) }} sub-menu item(s)</code>
                                                            </div>
                                                        @endif
                                                    </div>

                                                    @if($mainNavItem)
                                                        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                                <div><strong>Current Navigation Item:</strong></div>
                                                                <div>Name: {{ $mainNavItem->name }}</div>
                                                                <div>Label: {{ $mainNavItem->label }}</div>
                                                                <div>Route: {{ $mainNavItem->route ?: 'None (parent only)' }}</div>
                                                                <div>Sort Order: {{ $mainNavItem->sort_order }}</div>
                                                                <div>Status: {{ $mainNavItem->is_active ? 'Active' : 'Inactive' }}</div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Sub Navigation Items -->
                                    @if(isset($navConfig['subnav']) && !empty($navConfig['subnav']))
                                        <div class="ml-6">
                                            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Sub-menu Items:</h5>
                                            <div class="space-y-3">
                                                @foreach($navConfig['subnav'] as $index => $subItem)
                                                    @php
                                                        $existingSubItem = $existingNavItems->first(function($existing) use ($subItem) {
                                                            return $existing->route === ($subItem['route'] ?? '') ||
                                                                   $existing->label === ($subItem['label'] ?? '');
                                                        });
                                                    @endphp

                                                    <div class="p-3 bg-white dark:bg-gray-600 rounded border-l-4 border-blue-200">
                                                        <div class="flex items-start justify-between">
                                                            <div class="flex items-start">
                                                                <i class="{{ $subItem['icon'] ?? 'fas fa-circle' }} text-green-600 dark:text-green-400 mr-3 mt-1"></i>
                                                                <div class="flex-1">
                                                                    <div class="flex items-center space-x-2 mb-2">
                                                                        <h6 class="text-sm font-medium text-gray-900 dark:text-white">
                                                                            {{ $subItem['label'] ?? 'No label' }}
                                                                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(Sub-item {{ $index + 1 }})</span>
                                                                        </h6>
                                                                        @if($existingSubItem)
                                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                                                <i class="fas fa-check mr-1"></i>
                                                                                Exists
                                                                            </span>
                                                                        @else
                                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                                                                Missing
                                                                            </span>
                                                                        @endif
                                                                    </div>

                                                                    <div class="grid grid-cols-2 gap-4 text-xs text-gray-600 dark:text-gray-400">
                                                                        <div>
                                                                            <span class="font-medium">Route:</span>
                                                                            <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ $subItem['route'] ?? 'Not specified' }}</code>
                                                                        </div>
                                                                        <div>
                                                                            <span class="font-medium">Icon:</span>
                                                                            <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ $subItem['icon'] ?? 'Not specified' }}</code>
                                                                        </div>
                                                                        @if(isset($subItem['permissions']) && !empty($subItem['permissions']))
                                                                            <div class="col-span-2">
                                                                                <span class="font-medium">Permissions:</span>
                                                                                <code class="bg-gray-100 dark:bg-gray-600 px-1 rounded">{{ implode(', ', $subItem['permissions']) }}</code>
                                                                            </div>
                                                                        @endif
                                                                    </div>

                                                                    @if($existingSubItem)
                                                                        <div class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-500">
                                                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                                                <div>Name: {{ $existingSubItem->name }}</div>
                                                                                <div>Route: {{ $existingSubItem->route ?? 'None' }}</div>
                                                                                <div>Parent: {{ $existingSubItem->parent_id ? ($existingSubItem->parent->label ?? 'Unknown') : 'None' }}</div>
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                    <div class="flex items-start">
                                        <i class="fas fa-info-circle text-green-600 dark:text-green-400 mr-2 mt-0.5"></i>
                                        <div class="text-sm text-green-800 dark:text-green-200">
                                            <strong>Sync Navigation</strong> will:
                                            <ul class="mt-1 ml-4 list-disc text-xs">
                                                <li>Create or update the navigation menu item for this plugin</li>
                                                <li>Set the correct label, icon, and route from plugin configuration</li>
                                                <li>Apply the specified permissions for access control</li>
                                                <li>Position the item appropriately in the navigation menu</li>
                                                <li>Enable the menu item and make it visible</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="px-4 py-8 text-center">
                                <i class="fas fa-navigation text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Navigation Configuration</h4>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">This plugin doesn't have a navigation configuration defined in its config.json file.</p>

                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-4 text-left">
                                    <div class="flex items-start">
                                        <i class="fas fa-lightbulb text-blue-600 dark:text-blue-400 mr-2 mt-0.5"></i>
                                        <div class="text-sm text-blue-800 dark:text-blue-200">
                                            <strong>To add navigation:</strong>
                                            <ol class="mt-1 ml-4 list-decimal text-xs">
                                                <li>Add a "navigation" object or array to the plugin's config.json file</li>
                                                <li>Include properties like: label, icon, route, permissions, parent</li>
                                                <li>Examples:
                                                    <div class="mt-2 space-y-2">
                                                        <div>
                                                            <strong>Single navigation item:</strong>
                                                            <pre class="mt-1 bg-blue-100 dark:bg-blue-900/40 p-2 rounded text-xs overflow-x-auto"><code>"navigation": {
  "label": "My Plugin",
  "icon": "fas fa-puzzle-piece",
  "route": "myplugin.index",
  "permissions": ["view_myplugin"]
}</code></pre>
                                                        </div>
                                                        <div>
                                                            <strong>Navigation with sub-menu items:</strong>
                                                            <pre class="mt-1 bg-blue-100 dark:bg-blue-900/40 p-2 rounded text-xs overflow-x-auto"><code>"navigation": {
  "label": "My Plugin",
  "icon": "fas fa-puzzle-piece",
  "route": "",
  "permissions": ["view_myplugin"],
  "subnav": [
    {
      "label": "Dashboard",
      "icon": "fas fa-tachometer-alt",
      "route": "myplugin.index",
      "permissions": ["view_myplugin"]
    },
    {
      "label": "Settings",
      "icon": "fas fa-cog",
      "route": "myplugin.settings",
      "permissions": ["manage_myplugin"]
    }
  ]
}</code></pre>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>Refresh this page and use the "Sync Navigation" button</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Migration Management -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Plugin Migrations</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Database migrations for this plugin</p>
                            </div>
                            @php
                                $migrationPath = $plugin->getMigrationsPath();
                                $hasMigrations = is_dir($migrationPath) && count(glob($migrationPath . '/*.php')) > 0;
                            @endphp
                            @if($hasMigrations)
                                <div class="flex space-x-2">
                                    <button onclick="runPluginMigrations('{{ $plugin->name }}')"
                                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                        <i class="fas fa-database mr-2"></i>
                                        Run Migrations
                                    </button>
                                    <button onclick="rollbackPluginMigrations('{{ $plugin->name }}')"
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                                        <i class="fas fa-undo mr-2"></i>
                                        Rollback
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        @if($hasMigrations)
                            <div class="px-4 py-4">
                                @php
                                    $migrationFiles = glob($migrationPath . '/*.php');
                                    sort($migrationFiles);
                                @endphp

                                <div class="space-y-3">
                                    @foreach($migrationFiles as $migrationFile)
                                        @php
                                            $fileName = basename($migrationFile);
                                            $migrationName = pathinfo($fileName, PATHINFO_FILENAME);

                                            // Check if migration has been run
                                            $hasRun = DB::table('migrations')->where('migration', $migrationName)->exists();
                                        @endphp

                                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border">
                                            <div class="flex items-center">
                                                <i class="fas fa-file-code text-blue-600 dark:text-blue-400 mr-3"></i>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $fileName }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        Migration: {{ $migrationName }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                @if($hasRun)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                        <i class="fas fa-check mr-1"></i>
                                                        Migrated
                                                    </span>
                                                    @php
                                                        $migrationRecord = DB::table('migrations')->where('migration', $migrationName)->first();
                                                    @endphp
                                                    @if($migrationRecord)
                                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                                            Batch: {{ $migrationRecord->batch }}
                                                        </span>
                                                    @endif
                                                @else
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Pending
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                    <div class="flex items-start">
                                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2 mt-0.5"></i>
                                        <div class="text-sm text-blue-800 dark:text-blue-200">
                                            <strong>Migration Actions:</strong>
                                            <ul class="mt-1 ml-4 list-disc text-xs">
                                                <li><strong>Run Migrations:</strong> Execute all pending migrations for this plugin</li>
                                                <li><strong>Rollback:</strong> Rollback the last batch of migrations for this plugin</li>
                                                <li>Green status = Already migrated, Yellow status = Pending migration</li>
                                                <li>Migrations are run in chronological order based on filename</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="px-4 py-8 text-center">
                                <i class="fas fa-database text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Migrations Found</h4>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">This plugin doesn't have any database migrations.</p>

                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-4 text-left">
                                    <div class="flex items-start">
                                        <i class="fas fa-lightbulb text-blue-600 dark:text-blue-400 mr-2 mt-0.5"></i>
                                        <div class="text-sm text-blue-800 dark:text-blue-200">
                                            <strong>To add migrations:</strong>
                                            <ol class="mt-1 ml-4 list-decimal text-xs">
                                                <li>Create a "Migrations" folder in the plugin directory</li>
                                                <li>Add migration files with Laravel migration format</li>
                                                <li>Use timestamp naming: YYYY_MM_DD_HHMMSS_migration_name.php</li>
                                                <li>Example: 2025_08_02_000001_create_plugin_table.php</li>
                                                <li>Refresh this page and use the migration buttons</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Danger Zone -->
                @if(!$plugin->isSystemPlugin())
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border-l-4 border-red-400 dark:border-red-600">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-red-900 dark:text-red-400">Danger Zone</h3>
                            <p class="mt-1 max-w-2xl text-sm text-red-600 dark:text-red-400">Irreversible and destructive actions.</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-red-900 dark:text-red-400">Uninstall this plugin</h4>
                                    <p class="text-sm text-red-600 dark:text-red-400">Permanently remove this plugin and optionally clear all its data.</p>
                                </div>
                                <button onclick="showUninstallModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-trash mr-2"></i>
                                    Uninstall Plugin
                                </button>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Plugin Structure -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Plugin Structure</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Available components</p>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-cog text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">Controllers</span>
                                </div>
                                @if($plugin->hasControllers())
                                    <i class="fas fa-check text-green-500 dark:text-green-400"></i>
                                @else
                                    <i class="fas fa-times text-gray-300 dark:text-gray-600"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-database text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">Models</span>
                                </div>
                                @if($plugin->hasModels())
                                    <i class="fas fa-check text-green-500 dark:text-green-400"></i>
                                @else
                                    <i class="fas fa-times text-gray-300 dark:text-gray-600"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-eye text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">Views</span>
                                </div>
                                @if($plugin->hasViews())
                                    <i class="fas fa-check text-green-500 dark:text-green-400"></i>
                                @else
                                    <i class="fas fa-times text-gray-300 dark:text-gray-600"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-arrow-up text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">Migrations</span>
                                </div>
                                @if($plugin->hasMigrations())
                                    <i class="fas fa-check text-green-500 dark:text-green-400"></i>
                                @else
                                    <i class="fas fa-times text-gray-300 dark:text-gray-600"></i>
                                @endif
                            </li>
                            <li class="px-4 py-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-seedling text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">Seeds</span>
                                </div>
                                @if($plugin->hasSeeds())
                                    <i class="fas fa-check text-green-500 dark:text-green-400"></i>
                                @else
                                    <i class="fas fa-times text-gray-300 dark:text-gray-600"></i>
                                @endif
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Data Management -->
                @if($plugin->enabled && ($hasSeeder || $totalDataCount > 0))
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                        <div class="px-4 py-5 sm:px-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Data Management</h3>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Manage plugin data and sample content</p>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700">
                            <!-- Plugin Statistics -->
                            @if(!empty($stats) && $totalDataCount > 0)
                                <div class="px-4 py-4 border-b border-gray-200 dark:border-gray-700">
                                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Current Data</h4>
                                    <div class="grid grid-cols-2 gap-3">
                                        @foreach($stats as $key => $count)
                                            @if($count > 0)
                                                <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded border dark:border-gray-600">
                                                    <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ $count }}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ ucfirst(str_replace('_', ' ', $key)) }}</div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Data Management Actions -->
                            <div class="px-4 py-4 space-y-3">
                                @if($hasSeeder)
                                    <button onclick="seedPluginData('{{ $plugin->name }}', '{{ $seederClass }}')"
                                            class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                                        <i class="fas fa-seedling mr-2"></i>
                                        Seed Sample Data
                                    </button>
                                @endif

                                @if($totalDataCount > 0)
                                    <button onclick="clearPluginData('{{ $plugin->name }}', {{ json_encode($tables) }})"
                                            class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                                        <i class="fas fa-trash mr-2"></i>
                                        Clear All Data
                                    </button>
                                @endif

                                @if(!$hasSeeder && $totalDataCount === 0)
                                    <div class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        No data management options available
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700">
                        <div class="px-4 py-4 space-y-3">
                            <a href="{{ route('plugins.index') }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back to Plugins
                            </a>
                        </div>
                    </div>
                </div>


            </div>
    </div>

<!-- Uninstall Modal -->
<div id="uninstallModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border dark:border-gray-600 w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-center mx-auto w-12 h-12 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"></i>
            </div>
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Uninstall Plugin</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Are you sure you want to uninstall the <strong>{{ $plugin->name }}</strong> plugin? This action cannot be undone.
                    </p>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="clearDataCheckbox" class="rounded border-gray-300 dark:border-gray-600 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 dark:bg-gray-700">
                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Also clear all plugin data from database</span>
                        </label>
                    </div>
                </div>
                <div class="items-center px-4 py-3">
                    <button onclick="hideUninstallModal()" class="px-4 py-2 bg-gray-500 dark:bg-gray-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                    <button onclick="confirmUninstall()" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                        Uninstall
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Sync plugin permissions
function syncPluginPermissions(pluginName) {
    if (confirm(`This will create/update permissions for the ${pluginName} plugin and assign them to the admin role. Continue?`)) {
        showLoadingState('syncing permissions', pluginName);

        fetch(`/plugins/${pluginName}/sync-permissions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Sync plugin navigation
function syncPluginNavigation(pluginName) {
    if (confirm(`This will create/update the navigation menu item for the ${pluginName} plugin. Continue?`)) {
        showLoadingState('syncing navigation', pluginName);

        fetch(`/plugins/${pluginName}/sync-navigation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Run plugin migrations
function runPluginMigrations(pluginName) {
    if (confirm(`This will run all pending migrations for the ${pluginName} plugin. This action cannot be undone easily. Continue?`)) {
        showLoadingState('running migrations', pluginName);

        fetch(`/plugins/${pluginName}/run-migrations`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Rollback plugin migrations
function rollbackPluginMigrations(pluginName) {
    if (confirm(`This will rollback the last batch of migrations for the ${pluginName} plugin. This will undo database changes. Continue?`)) {
        showLoadingState('rolling back migrations', pluginName);

        fetch(`/plugins/${pluginName}/rollback-migrations`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Seed plugin data
function seedPluginData(pluginName, seederClass) {
    if (confirm(`This will create sample data for the ${pluginName} plugin. Continue?`)) {
        showLoadingState('seeding', pluginName);

        fetch(`/plugins/${pluginName}/seed-sample-data`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingState();
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('Error: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingState();
            showToast('Error: ' + error.message, 'error');
        });
    }
}

// Clear plugin data
function clearPluginData(pluginName, tables) {
    if (confirm(`⚠️ WARNING: This will permanently delete ALL ${pluginName} data. This action cannot be undone. Are you absolutely sure?`)) {
        if (confirm(`This is your final confirmation. All ${pluginName} plugin data will be permanently deleted. Continue?`)) {
            showLoadingState('clearing', pluginName);

            fetch(`/plugins/${pluginName}/clear-all-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingState();
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showToast('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoadingState();
                showToast('Error: ' + error.message, 'error');
            });
        }
    }
}

// Loading state functions
function showLoadingState(action, pluginName) {
    if (!document.getElementById('plugin-loading-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'plugin-loading-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                    <span id="loading-text" class="text-gray-700 font-medium">Processing...</span>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    const loadingText = document.getElementById('loading-text');
    if (loadingText) {
        const actionText = action === 'seeding' ? 'Seeding sample data' : 'Clearing plugin data';
        loadingText.textContent = `${actionText} for ${pluginName}...`;
    }
}

function hideLoadingState() {
    const overlay = document.getElementById('plugin-loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// Toast notification functions
function showToast(message, type) {
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(toastContainer);
    }

    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

    toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg flex items-center max-w-sm transform transition-all duration-300 translate-x-full opacity-0`;
    toast.innerHTML = `
        <i class="fas ${icon} mr-2"></i>
        <span class="flex-1">${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;

    toastContainer.appendChild(toast);

    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => toast.remove(), 300);
        }
    }, 5000);
}

// Uninstall modal functions
function showUninstallModal() {
    document.getElementById('uninstallModal').classList.remove('hidden');
}

function hideUninstallModal() {
    document.getElementById('uninstallModal').classList.add('hidden');
}

function confirmUninstall() {
    const clearData = document.getElementById('clearDataCheckbox').checked;
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/plugins/{{ $plugin->name }}/uninstall`;

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    if (clearData) {
        const clearDataInput = document.createElement('input');
        clearDataInput.type = 'hidden';
        clearDataInput.name = 'clear_data';
        clearDataInput.value = '1';
        form.appendChild(clearDataInput);
    }

    document.body.appendChild(form);
    form.submit();
}
</script>
@endsection
