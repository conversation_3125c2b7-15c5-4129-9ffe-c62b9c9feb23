<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PluginManager;
use App\Models\NavigationMenu;

class SyncNavigationFromPlugins extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'navigation:sync-plugins {--fresh : Remove existing plugin navigation items first}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync navigation menu items from plugin configurations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing navigation from plugin configurations...');

        if ($this->option('fresh')) {
            $this->info('Removing existing plugin navigation items...');
            NavigationMenu::whereNotNull('plugin')->delete();
        }

        $pluginManager = app(PluginManager::class);
        $plugins = $pluginManager->getAllPlugins();

        $created = 0;
        $updated = 0;

        foreach ($plugins as $pluginName => $plugin) {
            $configPath = $plugin->path . '/config.json';

            if (!file_exists($configPath)) {
                continue;
            }

            $config = json_decode(file_get_contents($configPath), true);

            if (!isset($config['navigation'])) {
                $this->warn("Plugin '{$pluginName}' has no navigation configuration");
                continue;
            }

            $navConfig = $config['navigation'];
            $result = $this->createNavigationItem($navConfig, $pluginName);

            if ($result['created']) {
                $created++;
            } else {
                $updated++;
            }

            $this->info("✓ Processed navigation for plugin: {$pluginName}");
        }

        // Clear navigation cache
        NavigationMenu::clearNavigationCache();

        $this->info("Navigation sync completed!");
        $this->info("Created: {$created} items");
        $this->info("Updated: {$updated} items");
    }

    /**
     * Create or update navigation item from config
     */
    private function createNavigationItem($navConfig, $pluginName, $parentId = null, $sortOrder = null): array
    {
        $name = $this->generateNavigationName($navConfig, $pluginName, $parentId);

        $data = [
            'name' => $name,
            'label' => $navConfig['label'],
            'icon' => $navConfig['icon'] ?? null,
            'route' => $navConfig['route'] ?? null,
            'url' => $navConfig['url'] ?? null,
            'plugin' => $pluginName,
            'permissions' => isset($navConfig['permissions']) ? $navConfig['permissions'] : null,
            'parent_id' => $parentId,
            'sort_order' => $sortOrder ?? ($navConfig['sort_order'] ?? 0),
            'is_active' => true,
            'visible' => true,
            'is_system' => false,
        ];

        $existing = NavigationMenu::where('name', $name)->first();

        if ($existing) {
            $existing->update($data);
            $created = false;
            $itemId = $existing->id;
        } else {
            $item = NavigationMenu::create($data);
            $created = true;
            $itemId = $item->id;
        }

        // Handle subnav items
        if (isset($navConfig['subnav']) && is_array($navConfig['subnav'])) {
            foreach ($navConfig['subnav'] as $index => $subnavConfig) {
                $this->createNavigationItem($subnavConfig, $pluginName, $itemId, $index + 1);
            }
        }

        return ['created' => $created, 'id' => $itemId];
    }

    /**
     * Generate unique navigation name
     */
    private function generateNavigationName($navConfig, $pluginName, $parentId = null): string
    {
        $baseName = $pluginName;

        if ($parentId) {
            $baseName .= '-' . strtolower(str_replace(' ', '-', $navConfig['label']));
        }

        return $baseName;
    }
}
