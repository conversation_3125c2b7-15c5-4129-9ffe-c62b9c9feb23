<?php

namespace App\Models;

class Plugin
{
    public string $name;
    public string $version;
    public bool $enabled;
    public array $dependencies;
    public string $description;
    public string $path;
    public array $config;
    public bool $system;
    public array $permissions;
    public string $category;

    public function __construct(array $config, string $path)
    {
        $this->name = $config['name'] ?? '';
        $this->version = $config['version'] ?? '1.0.0';
        $this->enabled = $config['enabled'] ?? false;
        $this->dependencies = $config['dependencies'] ?? [];
        $this->description = $config['description'] ?? '';
        $this->path = $path;
        $this->config = $config;
        $this->system = $config['system'] ?? false;
        $this->permissions = $config['permissions'] ?? [];
        $this->category = $this->determineCategory($config);
    }

    /**
     * Check if plugin has all required files
     */
    public function isValid(): bool
    {
        $requiredFiles = [
            'config.json',
            'routes.php'
        ];

        foreach ($requiredFiles as $file) {
            if (!file_exists($this->path . '/' . $file)) {
                return false;
            }
        }

        return !empty($this->name);
    }

    /**
     * Get plugin directories that should exist
     */
    public function getDirectories(): array
    {
        return [
            'Controllers',
            'Models',
            'Views',
            'Migrations',
            'Seeds'
        ];
    }

    /**
     * Check if plugin has controllers
     */
    public function hasControllers(): bool
    {
        $controllersPath = $this->path . '/Controllers';
        return is_dir($controllersPath) && count(glob($controllersPath . '/*.php')) > 0;
    }

    /**
     * Check if plugin is a system plugin
     */
    public function isSystemPlugin(): bool
    {
        return $this->system;
    }

    /**
     * Determine plugin category based on configuration and system status
     */
    private function determineCategory(array $config): string
    {
        // Check if category is explicitly defined in config
        if (isset($config['category'])) {
            return $config['category'];
        }

        // Auto-categorize based on system status
        if ($config['system'] ?? false) {
            return 'System';
        }

        return 'Third-party';
    }

    /**
     * Get plugin category
     */
    public function getCategory(): string
    {
        return $this->category;
    }

    /**
     * Check if user has any permissions for this plugin
     */
    public function userHasPermissions($user): bool
    {
        if (empty($this->permissions)) {
            return true; // If no permissions defined, allow access
        }

        if (!$user) {
            return false;
        }

        foreach ($this->permissions as $permission) {
            if ($user->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if plugin has models
     */
    public function hasModels(): bool
    {
        $modelsPath = $this->path . '/Models';
        return is_dir($modelsPath) && count(glob($modelsPath . '/*.php')) > 0;
    }

    /**
     * Check if plugin has views
     */
    public function hasViews(): bool
    {
        $viewsPath = $this->path . '/Views';
        return is_dir($viewsPath) && count(glob($viewsPath . '/*.blade.php')) > 0;
    }

    /**
     * Check if plugin has migrations
     */
    public function hasMigrations(): bool
    {
        $migrationsPath = $this->getMigrationsPath();
        return is_dir($migrationsPath) && count(glob($migrationsPath . '/*.php')) > 0;
    }

    /**
     * Check if plugin has seeds
     */
    public function hasSeeds(): bool
    {
        $seedsPath = $this->path . '/Seeds';
        return is_dir($seedsPath) && count(glob($seedsPath . '/*.php')) > 0;
    }

    /**
     * Get plugin routes file path
     */
    public function getRoutesPath(): string
    {
        return $this->path . '/routes.php';
    }

    /**
     * Get plugin views path
     */
    public function getViewsPath(): string
    {
        return $this->path . '/Views';
    }

    /**
     * Get plugin migrations path
     */
    public function getMigrationsPath(): string
    {
        // Check if migrations path is specified in config
        if (isset($this->config['migrations']) && $this->config['migrations']) {
            return $this->path . '/' . $this->config['migrations'];
        }

        // Default to Migrations directory
        return $this->path . '/Migrations';
    }

    /**
     * Get plugin seeds path
     */
    public function getSeedsPath(): string
    {
        return $this->path . '/Seeds';
    }

    /**
     * Convert to array for JSON serialization
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'version' => $this->version,
            'enabled' => $this->enabled,
            'dependencies' => $this->dependencies,
            'description' => $this->description,
            'path' => $this->path,
            'category' => $this->category,
            'system' => $this->system,
            'valid' => $this->isValid(),
            'has_controllers' => $this->hasControllers(),
            'has_models' => $this->hasModels(),
            'has_views' => $this->hasViews(),
            'has_migrations' => $this->hasMigrations(),
            'has_seeds' => $this->hasSeeds(),
        ];
    }
}
