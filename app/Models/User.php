<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user's role
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get the user's roles (many-to-many)
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $roleName): bool
    {
        if ($this->role && $this->role->name === $roleName) {
            return true;
        }

        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        foreach ($roles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permissionName): bool
    {
        // Use cached permissions to avoid repeated database queries
        $userPermissions = $this->getCachedPermissions();
        return in_array($permissionName, $userPermissions);
    }

    /**
     * Get all user permissions with caching
     */
    public function getCachedPermissions(): array
    {
        $cacheKey = 'user_permissions_' . $this->id;

        return cache()->remember($cacheKey, 300, function () { // Cache for 5 minutes
            $permissions = [];

            // Check direct role permissions
            if ($this->role) {
                $rolePermissions = $this->role->permissions()->pluck('name')->toArray();
                $permissions = array_merge($permissions, $rolePermissions);
            }

            // Check many-to-many role permissions
            $manyToManyPermissions = $this->roles()
                ->with('permissions')
                ->get()
                ->pluck('permissions')
                ->flatten()
                ->pluck('name')
                ->toArray();

            $permissions = array_merge($permissions, $manyToManyPermissions);

            return array_unique($permissions);
        });
    }

    /**
     * Clear cached permissions (call when user permissions change)
     */
    public function clearPermissionsCache(): void
    {
        $cacheKey = 'user_permissions_' . $this->id;
        cache()->forget($cacheKey);

        // Also clear navigation cache for this user
        cache()->forget('navigation_tree_' . $this->id);
    }

    /**
     * Boot method to clear cache when user changes
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($user) {
            $user->clearPermissionsCache();
        });

        static::deleted(function ($user) {
            $user->clearPermissionsCache();
        });
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Scope to get only active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the user's preferences
     */
    public function preferences()
    {
        return $this->hasOne(UserPreference::class);
    }

    /**
     * Get user preferences or create with defaults
     */
    public function getPreferences(): UserPreference
    {
        try {
            return UserPreference::getForUser($this);
        } catch (\Exception $e) {
            // If UserPreference table doesn't exist, return a new instance with defaults
            $instance = new UserPreference();
            $instance->fill(UserPreference::getDefaults());
            $instance->user_id = $this->id;
            return $instance;
        }
    }

    /**
     * Check if user has dark mode enabled
     */
    public function isDarkMode(): bool
    {
        try {
            return $this->getPreferences()->isDarkMode();
        } catch (\Exception $e) {
            // Fallback to session if database is not available
            $sessionTheme = session('user_theme', 'light');
            return $sessionTheme === 'dark';
        }
    }

    /**
     * Get theme class for HTML
     */
    public function getThemeClass(): string
    {
        try {
            return $this->getPreferences()->getThemeClass();
        } catch (\Exception $e) {
            // Fallback to session if database is not available
            $sessionTheme = session('user_theme', 'light');
            return $sessionTheme === 'dark' ? 'dark' : '';
        }
    }

    /**
     * Get the businesses this user is assigned to
     */
    public function businesses()
    {
        return $this->belongsToMany(\Plugins\Business\Models\Business::class, 'business_users')
                    ->withPivot('role')
                    ->withTimestamps();
    }

    /**
     * Get businesses created by this user
     */
    public function createdBusinesses()
    {
        return $this->hasMany(\Plugins\Business\Models\Business::class, 'created_by');
    }
}
