<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
    ];

    /**
     * Get the users that belong to this role
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the users that belong to this role (many-to-many)
     */
    public function usersMany()
    {
        return $this->belongsToMany(User::class)->withTimestamps();
    }

    /**
     * Get the permissions for this role
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class)->withTimestamps();
    }

    /**
     * Check if role has a specific permission
     */
    public function hasPermission(string $permissionName): bool
    {
        $rolePermissions = $this->getCachedPermissions();
        return in_array($permissionName, $rolePermissions);
    }

    /**
     * Get all role permissions with caching
     */
    public function getCachedPermissions(): array
    {
        $cacheKey = 'role_permissions_' . $this->id;

        return cache()->remember($cacheKey, 300, function () { // Cache for 5 minutes
            return $this->permissions()->pluck('name')->toArray();
        });
    }

    /**
     * Clear cached permissions (call when role permissions change)
     */
    public function clearPermissionsCache(): void
    {
        $cacheKey = 'role_permissions_' . $this->id;
        cache()->forget($cacheKey);

        // Also clear user permissions cache for all users with this role
        $this->users()->each(function ($user) {
            $user->clearPermissionsCache();
        });

        $this->usersMany()->each(function ($user) {
            $user->clearPermissionsCache();
        });
    }

    /**
     * Boot method to clear cache when role changes
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($role) {
            $role->clearPermissionsCache();
        });

        static::deleted(function ($role) {
            $role->clearPermissionsCache();
        });
    }

    /**
     * Give permission to role
     */
    public function givePermissionTo(Permission $permission)
    {
        return $this->permissions()->attach($permission);
    }

    /**
     * Revoke permission from role
     */
    public function revokePermissionTo(Permission $permission)
    {
        return $this->permissions()->detach($permission);
    }
}
