<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;
use App\Services\PluginManager;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Load plugin routes
        $this->loadPluginRoutes();

        // Share the navigation menu and enabled plugins with all views
        View::composer('*', function ($view) {
            // Share navigation menu from NavigationMenu model
            try {
                if (class_exists('\App\Models\NavigationMenu')) {
                    $user = auth()->user();

                    // Create cache key based on user ID and enabled plugins
                    $cacheKey = 'navigation_tree_' . ($user ? $user->id : 'guest');

                    $navigationTree = cache()->remember($cacheKey, 300, function () use ($user) { // Cache for 5 minutes
                        // Use the working navigation tree method
                        return \App\Models\NavigationMenu::getNavigationTree($user);
                    });

                    $view->with('navigationTree', $navigationTree);
                } else {
                    $view->with('navigationTree', []);
                }
            } catch (\Exception $e) {
                $view->with('navigationTree', []);
            }

            // Share enabled plugins with all views (cached)
            try {
                $enabledPlugins = cache()->remember('enabled_plugins', 300, function () {
                    $pluginManager = app(PluginManager::class);
                    return $pluginManager->getEnabledPlugins();
                });
                $view->with('enabledPlugins', $enabledPlugins);
            } catch (\Exception $e) {
                $view->with('enabledPlugins', []);
            }
        });
    }

    /**
     * Load routes from enabled plugins with proper middleware
     */
    protected function loadPluginRoutes(): void
    {
        try {
            $pluginManager = app(PluginManager::class);
            $enabledPlugins = $pluginManager->getEnabledPlugins();

            foreach ($enabledPlugins as $plugin) {
                // Load plugin service provider or plugin class if it exists
                $this->loadPluginServiceProvider($plugin);

                // Load plugin routes with web and auth middleware
                $routesPath = $plugin->path . '/routes.php';
                if (File::exists($routesPath)) {
                    \Illuminate\Support\Facades\Route::middleware(['web', 'auth'])
                        ->group($routesPath);
                }
            }
        } catch (\Exception $e) {
            // Silently fail if plugin manager is not available during bootstrap
        }
    }

    /**
     * Load plugin service provider or plugin class
     */
    protected function loadPluginServiceProvider($plugin): void
    {
        // Check for NavigationPlugin.php (for navigation plugin)
        $pluginClassPath = $plugin->path . '/' . ucfirst($plugin->name) . 'Plugin.php';
        if (File::exists($pluginClassPath)) {
            $className = 'Plugins\\' . ucfirst($plugin->name) . '\\' . ucfirst($plugin->name) . 'Plugin';
            if (class_exists($className)) {
                $pluginInstance = new $className($this->app);
                if (method_exists($pluginInstance, 'boot')) {
                    $pluginInstance->boot();
                }
            }
        }

        // Check for ServiceProvider.php (for other plugins)
        $serviceProviderPath = $plugin->path . '/' . ucfirst($plugin->name) . 'ServiceProvider.php';
        if (File::exists($serviceProviderPath)) {
            $className = 'Plugins\\' . ucfirst($plugin->name) . '\\' . ucfirst($plugin->name) . 'ServiceProvider';
            if (class_exists($className)) {
                $this->app->register($className);
            }
        }
    }
}
