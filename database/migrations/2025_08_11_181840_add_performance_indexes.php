<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to users table for performance
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                // Index for active users lookup
                try {
                    $table->index('is_active');
                } catch (\Exception $e) {
                    // Index might already exist
                }

                // Composite index for role and active status
                try {
                    $table->index(['role_id', 'is_active']);
                } catch (\Exception $e) {
                    // Index might already exist
                }
            });
        }

        // Add indexes to cache table for performance
        if (Schema::hasTable('cache')) {
            Schema::table('cache', function (Blueprint $table) {
                // Index for cache expiration cleanup
                try {
                    $table->index('expires_at');
                } catch (\Exception $e) {
                    // Index might already exist
                }
            });
        }

        // Add indexes to sessions table if it exists
        if (Schema::hasTable('sessions')) {
            Schema::table('sessions', function (Blueprint $table) {
                // Index for session cleanup
                try {
                    $table->index('last_activity');
                } catch (\Exception $e) {
                    // Index might already exist
                }

                // Index for user sessions lookup
                try {
                    $table->index('user_id');
                } catch (\Exception $e) {
                    // Index might already exist
                }
            });
        }

        // Add indexes to jobs table for queue performance
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                // Composite index for queue processing
                try {
                    $table->index(['queue', 'available_at']);
                } catch (\Exception $e) {
                    // Index might already exist
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes from users table
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex(['is_active']);
                $table->dropIndex(['role_id', 'is_active']);
            });
        }

        // Remove indexes from cache table
        if (Schema::hasTable('cache')) {
            Schema::table('cache', function (Blueprint $table) {
                $table->dropIndex(['expires_at']);
            });
        }

        // Remove indexes from sessions table
        if (Schema::hasTable('sessions')) {
            Schema::table('sessions', function (Blueprint $table) {
                $table->dropIndex(['last_activity']);
                $table->dropIndex(['user_id']);
            });
        }

        // Remove indexes from jobs table
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->dropIndex(['queue', 'available_at']);
            });
        }
    }


};
